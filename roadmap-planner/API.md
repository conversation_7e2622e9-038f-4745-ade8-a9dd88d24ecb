# API Documentation

This document describes the REST API endpoints for the Roadmap Planner application.

## Base URL

```
http://localhost:8080
```

## Authentication

The API uses Jira credentials passed via HTTP headers for authentication:

```
X-Jira-Username: your-username
X-Jira-Password: your-api-token
X-Jira-BaseURL: https://your-jira-instance.atlassian.net
X-Jira-Project: DEVOPS
```

## Endpoints

### Health Check

#### GET /health

Returns the health status of the API.

**Response:**
```json
{
  "status": "healthy",
  "service": "roadmap-planner"
}
```

### Authentication

#### POST /api/auth/login

Authenticate with <PERSON><PERSON> credentials.

**Request Body:**
```json
{
  "username": "your-username",
  "password": "your-api-token",
  "base_url": "https://your-jira-instance.atlassian.net"
}
```

**Response:**
```json
{
  "message": "Authentication successful",
  "user": "your-username"
}
```

#### POST /api/auth/logout

Clear authentication session.

**Response:**
```json
{
  "message": "Logout successful"
}
```

#### GET /api/auth/status

Check authentication status.

**Response:**
```json
{
  "authenticated": true,
  "user": "your-username"
}
```

### Roadmap Data

#### GET /api/roadmap

Get complete roadmap data including pillars, milestones, and epics.

**Response:**
```json
{
  "pillars": [
    {
      "id": "123",
      "key": "DEVOPS-123",
      "name": "Tool Integration",
      "priority": "High",
      "component": "connectors-operator",
      "milestones": [
        {
          "id": "456",
          "key": "DEVOPS-456",
          "name": "Q1 Integration Milestone",
          "quarter": "2025Q1",
          "pillar_id": "123",
          "status": "In Progress",
          "epics": [
            {
              "id": "789",
              "key": "DEVOPS-789",
              "name": "Implement OAuth Integration",
              "version": "connectors-operator-1.2.0",
              "component": "connectors-operator",
              "milestone_id": "456",
              "status": "To Do",
              "priority": "High"
            }
          ]
        }
      ]
    }
  ],
  "quarters": ["2025Q1", "2025Q2", "2025Q3", "2025Q4", "2026Q1", "2026Q2", "2026Q3", "2026Q4"]
}
```

#### GET /api/pillars

Get all pillars with their milestones and epics.

**Response:**
```json
{
  "pillars": [
    {
      "id": "123",
      "key": "DEVOPS-123",
      "name": "Tool Integration",
      "priority": "High",
      "component": "connectors-operator",
      "milestones": [...]
    }
  ]
}
```

### Milestones

#### POST /api/milestones

Create a new milestone.

**Request Body:**
```json
{
  "name": "Q2 Integration Milestone",
  "quarter": "2025Q2",
  "pillar_id": "123"
}
```

**Response:**
```json
{
  "id": "457",
  "key": "DEVOPS-457",
  "name": "Q2 Integration Milestone",
  "quarter": "2025Q2",
  "pillar_id": "123",
  "status": "To Do",
  "epics": []
}
```

### Epics

#### POST /api/epics

Create a new epic.

**Request Body:**
```json
{
  "name": "Implement SAML Integration",
  "component": "connectors-operator",
  "version": "connectors-operator-1.3.0",
  "priority": "High",
  "milestone_id": "456"
}
```

**Response:**
```json
{
  "id": "790",
  "key": "DEVOPS-790",
  "name": "Implement SAML Integration",
  "version": "connectors-operator-1.3.0",
  "component": "connectors-operator",
  "milestone_id": "456",
  "status": "To Do",
  "priority": "High"
}
```

#### PUT /api/epics/:id/milestone

Move an epic to a different milestone.

**URL Parameters:**
- `id`: Epic ID

**Request Body:**
```json
{
  "milestone_id": "457"
}
```

**Response:**
```json
{
  "message": "Epic milestone updated successfully"
}
```

### Components

#### GET /api/components/:name/versions

Get available versions for a component.

**URL Parameters:**
- `name`: Component name (e.g., "connectors-operator")

**Response:**
```json
{
  "versions": [
    "connectors-operator-1.0.0",
    "connectors-operator-1.1.0",
    "connectors-operator-1.2.0",
    "connectors-operator-1.3.0"
  ]
}
```

## Error Responses

All endpoints may return error responses in the following format:

```json
{
  "error": "Error message describing what went wrong"
}
```

### Common HTTP Status Codes

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required or invalid
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

## Data Models

### Pillar

```json
{
  "id": "string",
  "key": "string",
  "name": "string",
  "priority": "string",
  "component": "string",
  "milestones": "Milestone[]"
}
```

### Milestone

```json
{
  "id": "string",
  "key": "string",
  "name": "string",
  "quarter": "string",
  "pillar_id": "string",
  "status": "string",
  "epics": "Epic[]"
}
```

### Epic

```json
{
  "id": "string",
  "key": "string",
  "name": "string",
  "version": "string",
  "component": "string",
  "milestone_id": "string",
  "status": "string",
  "priority": "string"
}
```

## Rate Limiting

The API respects Jira's rate limiting. If you encounter rate limit errors:

- Reduce the frequency of requests
- Implement exponential backoff
- Cache responses when possible

## Examples

### cURL Examples

**Authenticate:**
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your-username",
    "password": "your-api-token",
    "base_url": "https://your-jira-instance.atlassian.net"
  }'
```

**Get roadmap data:**
```bash
curl -X GET http://localhost:8080/api/roadmap \
  -H "X-Jira-Username: your-username" \
  -H "X-Jira-Password: your-api-token" \
  -H "X-Jira-BaseURL: https://your-jira-instance.atlassian.net" \
  -H "X-Jira-Project: DEVOPS"
```

**Create milestone:**
```bash
curl -X POST http://localhost:8080/api/milestones \
  -H "Content-Type: application/json" \
  -H "X-Jira-Username: your-username" \
  -H "X-Jira-Password: your-api-token" \
  -H "X-Jira-BaseURL: https://your-jira-instance.atlassian.net" \
  -H "X-Jira-Project: DEVOPS" \
  -d '{
    "name": "Q2 Integration Milestone",
    "quarter": "2025Q2",
    "pillar_id": "123"
  }'
```

### JavaScript Examples

**Using fetch API:**
```javascript
// Set up headers
const headers = {
  'Content-Type': 'application/json',
  'X-Jira-Username': 'your-username',
  'X-Jira-Password': 'your-api-token',
  'X-Jira-BaseURL': 'https://your-jira-instance.atlassian.net',
  'X-Jira-Project': 'DEVOPS'
};

// Get roadmap data
const response = await fetch('/api/roadmap', { headers });
const roadmapData = await response.json();

// Create epic
const epicData = {
  name: 'New Epic',
  component: 'connectors-operator',
  milestone_id: '456'
};

const createResponse = await fetch('/api/epics', {
  method: 'POST',
  headers,
  body: JSON.stringify(epicData)
});
```
